<header class="l-header">
    <nav class="nav bd-grid">
        <div>
            <a href="#" class="nav-logo">Easy Prod</a>
        </div>

        <div class="nav-menu" id="nav-menu">
            <ul class="nav-list">
                <li class="nav-item"><a href="#home" class="nav-link active">Главное</a></li> 
                <li class="nav-item"><a href="#about" class="nav-link">О нас</a></li>           
                <li class="nav-item"><a href="#portfolio" class="nav-link">Наши проекты</a></li>
                <li class="nav-item"><a href="#contact" class="nav-link">Контакты</a></li>
                <li class="nav-item"><a routerLink="/" class="nav-link ">Наша Школа</a></li>
            </ul>
        </div>

        <div class="nav-toggle" id="nav-toggle">
            <i class='bx bx-menu'></i>
        </div>
    </nav>
</header>

<main class="l-main">
    <section class="home" id="home">
        <div class="home-container bd-grid">
            <h1 class="home-title"><span>Создаем сайты</span><br>и приложения<br>
                <a href="#contact" class="button-main">Связаться</a>
            </h1>
            
            <div class="home-scroll">
                <a href="#about" class="home-scroll-link"><i class='bx bx-up-arrow-alt' ></i>О нас</a>
            </div>

            <div class="ai-bot home-img" >
                <div class="head">
                  <div class="face">
                    <div class="eyes"> </div>
                    <div class="mouth"> </div>
                  </div>
                </div>
            </div>
        </div>
    </section>

    <section class="about section" id="about">
        <h2 class="section-title">О нас</h2>

        <div class="about-container bd-grid">
            <div class="about-img">
                <img src="assets/studio/img/1.png" alt="Easy Prod image">
            </div>

            <div>
                <h2 class="about-subtitle">Easy Prod</h2>
                <span class="about-profession">Мы — независимая студия цифрового продакшна. Создаём сайты, мобильные приложения, чат-боты, веб-сервисы, интерфейсы, кастомные CRM, трекеры и любые digital-решения, которые помогают бизнесу, людям и проектам быть в онлайне уверенно и удобно.</span>
                <p class="about-text">У нас за плечами — коммерческие платформы, стартапы, интерактивные игры, сервисы для мероприятий и нестандартные приложения. Мы не боимся сложных задач — наоборот, любим находить оригинальные и технологичные решения там, где шаблоны не работают.</p>
                <p class="about-text">Наша миссия — превращать идеи в реальные цифровые продукты, которые живут, работают и приносят результат</p>
            </div>   
        </div>
    </section>

    <section class="portfolio section" id="portfolio">
        <h2 class="section-title">Наши проекты</h2>
        <p style="text-align: center; margin-bottom: 40px;"><br>Мы также разрабатываем мобильные приложения, чат-боты и другие цифровые решения<br>расскажем о них при личной встрече или по запросу</p>

        <div class="portfolio-container bd-grid">
            <div class="portfolio-img">
                <img src="assets/studio/img/pro1.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт Интаго КЗ</h1>
                <p>Производственная компания</p>
                <div class="portfolio-link">
                    <a href="https://www.intagokazakhstan.kz/" class="portfolio-link-name" target="_blank">Посмотреть</a>
                </div>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro4.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт Del Force</h1>
                <p>Магазин Строй товаров</p>
                <div class="portfolio-link">
                    <a href="https://www.delforce.kz/" class="portfolio-link-name" target="_blank">Посмотреть</a>
                </div>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro3.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт Global TS</h1>
                <p>Строительная компания</p>

                <div class="portfolio-link">
                    <a href="https://globalserv.kz/" class="portfolio-link-name" target="_blank">Посмотреть</a>
                </div>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro2.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт USS 1</h1>
                <p>Строительная компания</p>

                <div class="portfolio-link">
                    <a href="https://www.uss1.kz/" class="portfolio-link-name" target="_blank">Посмотреть</a>
                </div>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro5.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Корпоративный LMS</h1>
                <p>Для управления образовательным процессом</p>
            </div>

            <div class="portfolio-img">
                <img src="assets/studio/img/pro6.png" alt="work image" loading="lazy">
                <h1 class="text-2xl leading-none">Сайт Exclusive Moving</h1>
                <p>Грузовая компания USA Los-Angeles</p>

                <div class="portfolio-link">
                    <a href="https://exclusivemovingla.com/" class="portfolio-link-name"  target="_blank">Посмотреть</a>
                </div>
            </div>

        </div>
    </section>
    
    <div id="sphe-app">
      <div class="sphe-hero">
        <div class="sphe-hero-text-background">
          <h1 class="sphe-title-1 text-center">Создаем лёгкость</h1>
          <h2 class="sphe-title-2 text-center">в цифровом мире</h2>
        </div>
      </div>

      <canvas #spheCanvas id="sphe-webgl-canvas"></canvas>
    </div>
    
    <section class="contact section" id="contact">
        <h2 class="section-title">Контакты</h2>

        <div class="contact-container bd-grid">
            <div class="contact-info">
                <h3 class="contact-subtitle">EMAIL</h3>
                <span class="contact-text">kopyrin7&#64;gmail.com</span>

                <h3 class="contact-subtitle">PHONE</h3>
                <span class="contact-text">****** 308 55 57</span>

                <h3 class="contact-subtitle">Whatsapp</h3>
                <span class="contact-text">****** 308 55 57</span>
            </div>

            <form class="contact-form" [formGroup]="contactForm" (ngSubmit)="onContactSubmit()">
                <!-- Success Message -->
                <div *ngIf="submitMessage" class="alert alert-success mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                    {{ submitMessage }}
                </div>

                <!-- Error Message -->
                <div *ngIf="submitError" class="alert alert-error mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    {{ submitError }}
                </div>

                <div class="contact-inputs">
                    <div class="input-group">
                        <input
                            type="text"
                            placeholder="Имя"
                            class="contact-input"
                            [class.error]="isFieldInvalid('name')"
                            formControlName="name"
                        >
                        <div *ngIf="isFieldInvalid('name')" class="error-message">
                            {{ getFieldError('name') }}
                        </div>
                    </div>

                    <div class="input-group">
                        <input
                            type="tel"
                            placeholder="+7 или 8XXXXXXXXXX"
                            class="contact-input"
                            [class.error]="isFieldInvalid('phone')"
                            formControlName="phone"
                            (input)="onPhoneInput($event)"
                            maxlength="12"
                        >
                        <div *ngIf="isFieldInvalid('phone')" class="error-message">
                            {{ getFieldError('phone') }}
                        </div>
                        <div class="help-text">
                            Формат: +7XXXXXXXXXX или 8XXXXXXXXXX
                        </div>
                    </div>
                </div>

                <div class="input-group">
                    <textarea
                        cols="0"
                        rows="10"
                        class="contact-input"
                        placeholder="Опишите ваш проект или задачу"
                        [class.error]="isFieldInvalid('message')"
                        formControlName="message"
                    ></textarea>
                    <div *ngIf="isFieldInvalid('message')" class="error-message">
                        {{ getFieldError('message') }}
                    </div>
                </div>

                <button
                    type="submit"
                    class="contact-button"
                    [disabled]="isSubmitting"
                    [class.loading]="isSubmitting"
                >
                    <span *ngIf="isSubmitting" class="spinner"></span>
                    {{ isSubmitting ? 'Отправка...' : 'Отправить заявку' }}
                </button>
            </form>
        </div>
    </section>

</main>

<footer class="footer section">
    <div class="footer-container bd-grid">
        <div class="footer-data">
            <h2 class="footer-title">Easy Prod</h2>
            <ul>
                <li><a href="#home" class="footer-link">Главная</a></li>
                <li><a href="#about" class="footer-link">О нас</a></li>                       
                <li><a href="#portfolio" class="footer-link">Наши проекты</a></li>
                <li><a href="#contact" class="footer-link">Контакты</a></li>
            </ul>
        </div>
    </div>
</footer>

<!-- Contact Popup -->
<app-contact-popup></app-contact-popup>
