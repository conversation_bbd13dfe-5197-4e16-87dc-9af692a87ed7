import { Compo<PERSON>, On<PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Title, Meta } from '@angular/platform-browser';
import { StudioRequestsService } from '../../../../core/services/studio-requests.service';
import { CreateStudioRequest } from '../../../../core/models/studio-request.models';
import { phoneValidator, formatPhoneInput, getPhoneErrorMessage } from '../../../../core/validators/phone.validator';

declare var ScrollReveal: any;

@Component({
  selector: 'app-studio-page',
  templateUrl: './studio-page.component.html',
  styleUrls: ['./studio-page.component.css'],
  standalone: false
})
export class StudioPageComponent implements OnInit, AfterViewInit, On<PERSON><PERSON>roy {
  @ViewChild('spheCanvas', { static: false }) spheCanvas!: ElementRef<HTMLCanvasElement>;

  private bg: any;
  private scrollReveal: any;

  // Contact form properties
  contactForm!: FormGroup;
  isSubmitting = false;
  submitMessage = '';
  submitError = '';

  constructor(
    private router: Router,
    private titleService: Title,
    private meta: Meta,
    private formBuilder: FormBuilder,
    private studioRequestsService: StudioRequestsService
  ) { }

  ngOnInit(): void {
    this.titleService.setTitle('Easy Prod - Студия разработки сайтов и приложений');
    this.meta.updateTag({
      name: 'description',
      content: 'Easy Prod - профессиональная студия разработки веб-сайтов, мобильных приложений и цифровых решений. Создаем современные, функциональные и красивые проекты для вашего бизнеса.'
    });
    this.initializeScrollReveal();
    this.initializeContactForm();
  }

  ngAfterViewInit(): void {
    this.initializeMobileMenu();
    this.initializeNavigation();
    this.initializeScrollActive();
    this.initializeScrollRevealAnimations();
    this.initializeThreeJSBackground();
  }

  ngOnDestroy(): void {
    if (this.bg) {
      // Clean up Three.js background if needed
    }
  }

  private initializeMobileMenu(): void {
    const toggle = document.getElementById('nav-toggle');
    const nav = document.getElementById('nav-menu');

    if (toggle && nav) {
      toggle.addEventListener('click', () => {
        nav.classList.toggle('show');
      });
    }
  }

  private initializeNavigation(): void {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
      link.addEventListener('click', (event) => {
        const target = event.target as HTMLAnchorElement;
        const routerLink = target.getAttribute('routerLink');
        const href = target.getAttribute('href');

        // Handle router navigation
        if (routerLink) {
          event.preventDefault();
          this.router.navigate([routerLink]);
          return;
        }

        // Handle anchor links
        if (href && href.startsWith('#')) {
          event.preventDefault();

          navLinks.forEach(n => n.classList.remove('active'));
          target.classList.add('active');

          const navMenu = document.getElementById('nav-menu');
          if (navMenu) {
            navMenu.classList.remove('show');
          }

          const targetId = href.substring(1);
          this.scrollToSection(targetId);
        }
      });
    });

    // Also handle other anchor links
    const allLinks = document.querySelectorAll('a[href^="#"]');
    allLinks.forEach(link => {
      if (!link.classList.contains('nav-link')) {
        link.addEventListener('click', (event) => {
          event.preventDefault();
          const href = (event.target as HTMLAnchorElement).getAttribute('href');
          if (href && href.startsWith('#')) {
            const targetId = href.substring(1);
            this.scrollToSection(targetId);
          }
        });
      }
    });
  }

  private initializeScrollActive(): void {
    const sections = document.querySelectorAll('section[id]');
    
    const scrollActiveHandler = () => {
      const scrollY = window.pageYOffset;

      sections.forEach(current => {
        const sectionHeight = current.clientHeight;
        const sectionTop = (current as HTMLElement).offsetTop - 50;
        const sectionId = current.getAttribute('id');

        if (scrollY > sectionTop && scrollY <= sectionTop + sectionHeight) {
          const activeLink = document.querySelector('.nav-menu a[href*=' + sectionId + ']');
          if (activeLink) {
            activeLink.classList.add('active');
          }
        } else {
          const inactiveLink = document.querySelector('.nav-menu a[href*=' + sectionId + ']');
          if (inactiveLink) {
            inactiveLink.classList.remove('active');
          }
        }
      });
    };

    window.addEventListener('scroll', scrollActiveHandler);
  }

  private initializeScrollReveal(): void {
    // ScrollReveal will be initialized after view init
  }

  private initializeScrollRevealAnimations(): void {
    if (typeof ScrollReveal !== 'undefined') {
      const sr = ScrollReveal({
        origin: 'top',
        distance: '80px',
        duration: 1000,
        reset: false
      });

      sr.reveal('.home-title', {});
      sr.reveal('.home-scroll', { delay: 200 });
      sr.reveal('.home-img', { origin: 'right', delay: 400 });

      sr.reveal('.about-img', { delay: 500 });
      sr.reveal('.about-subtitle', { delay: 300 });
      sr.reveal('.about-profession', { delay: 400 });
      sr.reveal('.about-text', { delay: 500 });
      sr.reveal('.about-social-icon', { delay: 600, interval: 200 });

      sr.reveal('.skills-subtitle', {});
      sr.reveal('.skills-name', { distance: '20px', delay: 50, interval: 100 });
      sr.reveal('.skills-img', { delay: 400 });

      sr.reveal('.portfolio-img', { interval: 200 });

      sr.reveal('.contact-subtitle', {});
      sr.reveal('.contact-text', { interval: 200 });
      sr.reveal('.contact-input', { delay: 400 });
      sr.reveal('.contact-button', { delay: 600 });
    }
  }

  private initializeThreeJSBackground(): void {
    // We'll load the Three.js background via script tag in index.html
    // For now, just set up the canvas and basic interaction
    const app = document.getElementById('sphe-app');
    const canvas = this.spheCanvas?.nativeElement;

    if (app && canvas) {
      // Add click handler for the app
      app.addEventListener('click', () => {
        if (this.bg) {
          this.bg.togglePause();
        }
      });

      // Try to initialize the background if the library is available
      setTimeout(() => {
        this.tryInitializeBackground();
      }, 1000);
    }
  }

  private tryInitializeBackground(): void {
    try {
      // Check if the global Spheres2Background is available
      const Spheres2Background = (window as any).Spheres2Background;

      if (Spheres2Background) {
        const canvas = this.spheCanvas?.nativeElement;
        if (canvas) {
          this.bg = Spheres2Background(canvas, {
            count: 200,
            colors: [0xff0000, 0x0, 0xffffff],
            minSize: 0.5,
            maxSize: 1
          });

          // Initialize with random colors
          this.randomColors();
        }
      }
    } catch (error) {
      console.warn('Three.js background not available:', error);
    }
  }

  private randomColors(): void {
    if (this.bg && this.bg.spheres) {
      this.bg.spheres.setColors([
        0xffffff * Math.random(),
        0xffffff * Math.random(),
        0xffffff * Math.random()
      ]);
      
      if (this.bg.spheres.light1) {
        this.bg.spheres.light1.color.set(0xffffff * Math.random());
      }
    }
  }

  // Initialize contact form
  private initializeContactForm(): void {
    this.contactForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      phone: ['', [Validators.required, phoneValidator()]],
      message: ['', [Validators.required, Validators.minLength(10)]]
    });
  }

  // Method to handle contact form submission
  onContactSubmit(): void {
    if (this.contactForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isSubmitting = true;
    this.submitMessage = '';
    this.submitError = '';

    const requestData: CreateStudioRequest = {
      name: this.contactForm.value.name,
      phone: this.contactForm.value.phone,
      message: this.contactForm.value.message,
      status: 'new'
    };

    this.studioRequestsService.createRequest(requestData).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        this.submitMessage = 'Ваша заявка успешно отправлена! Мы свяжемся с вами в ближайшее время.';
        this.contactForm.reset();

        // Google Ads conversion tracking
        this.trackGoogleAdsConversion();
      },
      error: (error) => {
        this.isSubmitting = false;
        this.handleSubmitError(error);
      }
    });
  }

  private handleSubmitError(error: any): void {
    if (error.status === 400) {
      this.submitError = 'Пожалуйста, проверьте введенные данные.';
    } else if (error.status === 0) {
      this.submitError = 'Ошибка подключения к серверу. Попробуйте позже.';
    } else {
      this.submitError = 'Произошла ошибка при отправке заявки. Попробуйте позже.';
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.contactForm.controls).forEach(key => {
      const control = this.contactForm.get(key);
      control?.markAsTouched();
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.contactForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.contactForm.get(fieldName);
    if (field?.errors) {
      if (fieldName === 'phone') {
        return getPhoneErrorMessage(field.errors);
      }
      if (field.errors['required']) {
        const fieldLabels: { [key: string]: string } = {
          name: 'Имя',
          phone: 'Телефон',
          message: 'Сообщение'
        };
        return `${fieldLabels[fieldName]} обязательно для заполнения`;
      }
      if (field.errors['minlength']) {
        const minLength = field.errors['minlength'].requiredLength;
        return `Минимальная длина: ${minLength} символов`;
      }
    }
    return '';
  }

  // Format phone number input
  onPhoneInput(event: any): void {
    const cleanValue = formatPhoneInput(event);

    // Update the form control value
    this.contactForm.get('phone')?.setValue(cleanValue, { emitEvent: false });

    // Update the input field display value
    event.target.value = cleanValue;
  }

  // Method to handle scroll to section
  scrollToSection(sectionId: string): void {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  // Google Ads conversion tracking
  private trackGoogleAdsConversion(): void {
    if (typeof (window as any).gtag === 'function') {
      (window as any).gtag('event', 'conversion', {
        'send_to': 'AW-17264732790/ZzD1CI-lxOlaEPbUu6hA'
      });
    }
  }
}
