<div class="phone-input__container">
  <label *ngIf="label" class="phone-input__label">{{ label }}</label>
  
  <div class="phone-input__wrapper" [class.phone-input__wrapper--error]="showError" [class.phone-input__wrapper--disabled]="disabled">
    <!-- Country Code Dropdown -->
    <div class="country-code__dropdown">
      <button 
        type="button"
        class="country-code__trigger"
        [class.country-code__trigger--open]="isDropdownOpen"
        [disabled]="disabled"
        (click)="toggleDropdown($event)">
        <img 
          [src]="selectedCountry.flag" 
          [alt]="selectedCountry.name"
          class="country-code__flag-img">
        <span class="country-code__value">{{ selectedCountry.dialCode }}</span>
        <span class="country-code__arrow" [class.country-code__arrow--open]="isDropdownOpen">▼</span>
      </button>
      
      <!-- Dropdown List -->
      <ul class="country-code__list" [class.country-code__list--open]="isDropdownOpen">
        <li 
          *ngFor="let country of countries"
          class="country-code__item"
          [class.country-code__item--active]="country.code === selectedCountry.code"
          (click)="selectCountry(country, $event)">
          <img 
            [src]="country.flag" 
            [alt]="country.name"
            class="country-flag-img">
          <span class="country-dial">{{ country.dialCode }}</span>
          <span class="country-name">{{ country.name }}</span>
        </li>
      </ul>
    </div>
    
    <!-- Phone Number Input -->
    <input 
      type="tel"
      class="phone-input__field"
      [placeholder]="getPlaceholder()"
      [disabled]="disabled"
      [value]="phoneNumber"
      (input)="onPhoneInput($event)"
      (blur)="onBlur()"
      autocomplete="tel">
  </div>
  
  <!-- Error Message -->
  <div *ngIf="showError && errorMessage" class="phone-input__error">
    {{ errorMessage }}
  </div>
</div>
