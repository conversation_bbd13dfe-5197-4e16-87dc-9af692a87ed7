import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainComponent } from './features/main/components/main-component/main-component';
import { StudioPageComponent } from './features/studio/components/studio-page/studio-page.component';
import { LoginComponent } from './features/auth/components/login/login.component';
import { DashboardComponent } from './features/admin/components/dashboard/dashboard.component';
import { PhoneDemoComponent } from './features/demo/phone-demo.component';
import { AuthGuard } from './core/guards/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: MainComponent
  },
  {
    path: 'studio',
    component: StudioPageComponent
  },
  {
    path: 'login',
    component: LoginComponent
  },
  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'phone-demo',
    component: PhoneDemoComponent
  },
  {
    path: '**',
    redirectTo: ''
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
