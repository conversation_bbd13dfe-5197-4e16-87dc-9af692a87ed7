import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { phoneValidator } from '../../core/validators/phone.validator';
import { Country } from '../../core/services/country.service';

@Component({
  selector: 'app-phone-demo',
  template: `
    <div class="container mx-auto p-8 max-w-2xl">
      <h1 class="text-3xl font-bold mb-8 text-center">Phone Input Component Demo</h1>
      
      <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Test Phone Input</h2>
        
        <form [formGroup]="demoForm" (ngSubmit)="onSubmit()">
          <div class="mb-6">
            <app-phone-input
              formControlName="phone"
              label="Номер телефона"
              [showError]="isFieldInvalid('phone')"
              [errorMessage]="getFieldError('phone')"
              [required]="true"
              (phoneChange)="onPhoneChange($event)"
              (countryChange)="onCountryChange($event)">
            </app-phone-input>
          </div>
          
          <button 
            type="submit"
            [disabled]="demoForm.invalid"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
            Submit Phone Number
          </button>
        </form>
      </div>
      
      <div class="bg-gray-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">Form State</h3>
        
        <div class="space-y-2 text-sm">
          <div><strong>Form Valid:</strong> {{ demoForm.valid ? 'Yes' : 'No' }}</div>
          <div><strong>Phone Value:</strong> {{ demoForm.get('phone')?.value || 'Empty' }}</div>
          <div><strong>Selected Country:</strong> {{ selectedCountry?.name || 'None' }}</div>
          <div><strong>Country Code:</strong> {{ selectedCountry?.dialCode || 'None' }}</div>
          <div><strong>Form Errors:</strong> {{ getFormErrors() }}</div>
        </div>
        
        <div class="mt-4">
          <h4 class="font-semibold mb-2">Supported Countries:</h4>
          <div class="grid grid-cols-2 gap-2 text-xs">
            <div>🇰🇿 Kazakhstan (+7)</div>
            <div>🇰🇬 Kyrgyzstan (+996)</div>
            <div>🇷🇺 Russia (+7)</div>
            <div>🇺🇿 Uzbekistan (+998)</div>
            <div>🇹🇯 Tajikistan (+992)</div>
            <div>🇹🇲 Turkmenistan (+993)</div>
            <div>🇧🇾 Belarus (+375)</div>
            <div>🇺🇦 Ukraine (+380)</div>
            <div>🇦🇿 Azerbaijan (+994)</div>
            <div>🇦🇲 Armenia (+374)</div>
            <div>🇬🇪 Georgia (+995)</div>
            <div>🇲🇩 Moldova (+373)</div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .container {
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
  `],
  standalone: false
})
export class PhoneDemoComponent implements OnInit {
  demoForm: FormGroup;
  selectedCountry: Country | null = null;

  constructor(private fb: FormBuilder) {
    this.demoForm = this.fb.group({
      phone: ['', [Validators.required, phoneValidator()]]
    });
  }

  ngOnInit() {
    // Subscribe to form changes for debugging
    this.demoForm.valueChanges.subscribe(value => {
      console.log('Form value changed:', value);
    });
  }

  onSubmit() {
    if (this.demoForm.valid) {
      alert(`Phone number submitted: ${this.demoForm.get('phone')?.value}`);
    } else {
      this.markFormGroupTouched();
    }
  }

  onPhoneChange(phoneNumber: string) {
    console.log('Phone changed:', phoneNumber);
  }

  onCountryChange(country: Country) {
    console.log('Country changed:', country);
    this.selectedCountry = country;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.demoForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.demoForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return 'Это поле обязательно для заполнения';
      }
      if (field.errors['invalidPhone']) {
        return 'Введите корректный номер телефона';
      }
    }
    return '';
  }

  getFormErrors(): string {
    const errors = this.demoForm.get('phone')?.errors;
    return errors ? JSON.stringify(errors) : 'None';
  }

  private markFormGroupTouched() {
    Object.keys(this.demoForm.controls).forEach(key => {
      const control = this.demoForm.get(key);
      control?.markAsTouched();
    });
  }
}
