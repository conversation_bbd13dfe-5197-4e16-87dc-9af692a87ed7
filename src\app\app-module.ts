import { NgModule, provideBrowserGlobalErrorListeners } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import {provideAnimationsAsync} from '@angular/platform-browser/animations/async';
import { AppRoutingModule } from './app-routing-module';
import { App } from './app';
import { MainComponent } from './features/main/components/main-component/main-component';
import { StudioPageComponent } from './features/studio/components/studio-page/studio-page.component';
import { LoginComponent } from './features/auth/components/login/login.component';
import { DashboardComponent } from './features/admin/components/dashboard/dashboard.component';
import { PhoneDemoComponent } from './features/demo/phone-demo.component';
import {LottieComponent, provideCacheableAnimationLoader, provideLottieOptions} from 'ngx-lottie';
import player from 'lottie-web';
import { SharedModule } from './shared/shared.module';


@NgModule({
  declarations: [
    App,
    MainComponent,
    StudioPageComponent,
    LoginComponent,
    DashboardComponent,
    PhoneDemoComponent
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    ReactiveFormsModule,
    HttpClientModule,
    AppRoutingModule,
    LottieComponent,
    SharedModule,
  ],
  providers: [
    provideBrowserGlobalErrorListeners(),
    provideAnimationsAsync(),
    provideLottieOptions({ player: () => player }),
  ],
  bootstrap: [App]
})
export class AppModule { }
